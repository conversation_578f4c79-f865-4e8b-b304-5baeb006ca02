import axios from "axios";
import { PromotionConfig, PromotionFilterParam } from "../Types/promotion-types";

const URL = import.meta.env.VITE_API_URL;
const GET_API_URL = `${URL}/get-promotions`;
const GET_PROMOTION_BY_FISCAL_URL = `${URL}/get-promotion-by-fiscal`;
const ADD_PROMOTION_URL = `${URL}/add-promotion`;
const UPDATE_PROMOTION_URL = `${URL}/update-promotion`;
const SEARCH_API_URL = `${URL}/fiscal-calendar/search`;
const CONFIG_URL = `${URL}/fiscal-calendar/config`;
const UPDATE_URL = `${URL}/update-fiscal-calendar`;
const ADD_MANUAL_URL = `${URL}/add-promotion`;
const BULK_UPLOAD_URL = `${URL}/bulk-upload-fiscal-calendar`;

// New interfaces for the updated API
export interface GetPromotionByFiscalParams {
  fiscal_year: number;
  fiscal_period: string;
  fiscal_week: string;
}

export interface PromotionDetail {
  fiscal_year: number;
  fiscal_period: string;
  fiscal_week: string;
  holidayAndEvents?: string;
  marketingPromotion?: string;
  loyaltyCampaigns?: string;
  lastYearHolidayAndEvents?: string;
  "current_year p-week Key": string;
}

export interface AddPromotionParams {
  fiscal_year: number;
  fiscal_period: string;
  fiscal_week: string;
  holidayAndEvents?: string;
  marketingPromotion?: string;
  loyaltyCampaigns?: string;
}

export interface UpdatePromotionParams {
  originalKey: string;
  fiscal_year: number;
  fiscal_period: string;
  fiscal_week: string;
  holidayAndEvents?: string;
  marketingPromotion?: string;
  loyaltyCampaigns?: string;
  lastYearHolidayAndEvents?: string;
  lastYearMarketingPromotion?: string;
  lastYearLoyaltyCampaigns?: string;
}

export interface GetPromotionByFiscalResponse {
  query: string;
  result: {
    fiscal_year: number;
    fiscal_period: string;
    fiscal_week: string;
    "Holiday and Events": string;
    "Marketing Pramotion": string;
    "Loyalty Campaigns": string;
    "Last Year Holiday and Events": string;
    "Last Year Marketing Promotion": string;
    "Last Year Loyalty Campaigns": string;
    "current_year p-week Key": string;
    "previous year  p/w": string;
  } | null;
}

export interface AddPromotionResponse {
  success: boolean;
  message: string;
  data?: PromotionDetail;
}

export interface UpdatePromotionResponse {
  success: boolean;
  message: string;
  data?: PromotionDetail;
}

interface PromotionDataResponse {
  query: string;
  result: {
    fiscal_year: number;
    fiscal_period: string;
    fiscal_week: string;
    "Holiday and Events": string;
    "Marketing Pramotion ": string;
    "Loyalty Campaigns": string;
    "Last Year Holiday and Events": string;
    "Last Year Marketing Promotion": string;
    "Last Year Loyalty Campaigns": string;
    "current_year p-week Key": string;
    "previous year  p/w": string;
  }[];
}

// New API functions
export const getPromotionByFiscal = async (params: GetPromotionByFiscalParams): Promise<GetPromotionByFiscalResponse> => {
  try {
    const response = await axios.get(GET_PROMOTION_BY_FISCAL_URL, { params });
    return response.data;
  } catch (error) {
    console.error("Error fetching promotion by fiscal:", error);
    return { query: "", result: null };
  }
};

export const addPromotion = async (params: AddPromotionParams): Promise<AddPromotionResponse> => {
  try {
    const response = await axios.post(ADD_PROMOTION_URL, params);
    return response.data;
  } catch (error) {
    console.error("Error adding promotion:", error);
    throw error;
  }
};

export const updatePromotion = async (params: UpdatePromotionParams): Promise<UpdatePromotionResponse> => {
  try {
    const response = await axios.put(UPDATE_PROMOTION_URL, params);
    return response.data;
  } catch (error) {
    console.error("Error updating promotion:", error);
    throw error;
  }
};

export const fetchPromotion = async (
  page: number,
  limit: number,
  filterParams: PromotionFilterParam,
  sortBy?: string | null,
  sortOrder?: "asc" | "desc" | null
) => {
  try {
    const filters: Record<string, string> = {};

    let body = {};
    body = { ...body, page, limit, sortBy, sortOrder };
    if (Object.keys(filters).length !== 0) {
      body = { ...body, filters };
    }

    const response = await axios.get(GET_API_URL, body);
    const data = response.data as PromotionDataResponse;

    return {
      result: data.result,
      totalCount: data.result.length,
      totalPages: Math.ceil(data.result.length / limit),
      currentPage: page,
      count: data.result.length,
    };
  } catch (error) {
    console.error(error);
    return {
      result: [],
      totalCount: 0,
      totalPages: 0,
      currentPage: 0,
      count: 0,
    };
  }
};

export const searchPromotion = async (searchText: string) => {
  try {
    const response = await axios.post(SEARCH_API_URL, { searchText });
    return response.data;
  } catch (error) {
    console.error(error);
    return [];
  }
};

export const fetchPromotionConfigData = async (): Promise<PromotionConfig> => {
  try {
    const response = await axios.get(CONFIG_URL);
    return response.data.config;
  } catch (error) {
    console.error("Error fetching config:", error);
    throw error;
  }
};

export interface UpdatePromotionRecordParams {
  promotionId: number;
  filters: PromotionUpdatedField[];
}
interface PromotionUpdatedField {
  field: string;
  value: string | number | boolean | null;
}

export const updatePromotionRecord = async (params: UpdatePromotionRecordParams) => {
  try {
    const response = await axios.put(UPDATE_URL, params);
    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export const addPromotionRecord = async (params: Record<string, string | number | boolean | null>) => {
  try {
    const response = await axios.post(ADD_MANUAL_URL, params);
    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
};

export const bulkUploadPromotionRecord = async (file: File) => {
  const formData = new FormData();
  formData.append("file", file);
  try {
    const response = await axios.post(BULK_UPLOAD_URL, formData, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  } catch (error) {
    console.error(error);
    throw error;
  }
};
