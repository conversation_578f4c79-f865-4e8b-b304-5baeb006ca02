import AddIcon from "@mui/icons-material/Add";
import { Box, Button, CircularProgress, Dialog, DialogActions, DialogContent, DialogTitle, Grid, Typography } from "@mui/material";
import { createTheme, ThemeProvider } from "@mui/material/styles";
import { FC, useCallback, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { initialAddPromotionData, setNewPromotion } from "../../../__redux/addPromotionSlice";
import { pushNewAlert, setFullScreenLoader } from "../../../__redux/generalSlice";
import { setLoading, setPromotions, setTotalPages } from "../../../__redux/promotionSlice";
import { addPromotion, updatePromotion, getPromotionByFiscal, fetchPromotion, fetchPromotionConfigData, PromotionDetail } from "../../api/promotionApi";
import { ROLES } from "../../constants/constants";
import { StyledButton, StyledPaper } from "../../CustomComponents/StyledComponents";
import { RootState } from "../../store";
import { PromotionFilterParam, PromotionObject } from "../../Types/promotion-types";
import { addSpaceToCamelCase } from "../ai/components/utils";
import AddPromotionTab from "./components/add-promotion/AddPromotionTab";
import PromotionTable from "./components/PromotionTable";

const theme = createTheme({
  palette: {
    primary: {
      main: "#007AFF",
    },
    background: {
      default: "#f5f5f5",
    },
  },
  typography: {
    fontFamily: "Roboto, sans-serif",
    h4: {
      fontWeight: 700,
      fontSize: "2rem",
    },
    h6: {
      fontWeight: 500,
      fontSize: "1.25rem",
    },
    body1: {
      fontSize: "1rem",
    },
  },
});

const Promotion: FC<{ tablename: string }> = ({ tablename }) => {
  const dispatch = useDispatch();
  const { promotions, loading, page, limit, filters } = useSelector((state: RootState) => state.promotion);
  const { newPromotion } = useSelector((state: RootState) => state.addPromotion);
  const { userRole } = useSelector((state: RootState) => state.auth);

  const [openAddManualPromotion, setOpenAddManualPromotion] = useState<boolean>(false);
  const [openEditPromotion, setOpenEditPromotion] = useState<boolean>(false);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [saveUpdateConfirmOpen, setSaveUpdateConfirmOpen] = useState<boolean>(false);
  const [saveAddConfirmOpen, setSaveAddConfirmOpen] = useState<boolean>(false);
  const [currentPromotionForEdit, setCurrentPromotionForEdit] = useState<PromotionDetail | null>(null);
  const [lastYearEventsForDialog, setLastYearEventsForDialog] = useState<string>("");
  const [lastYearMarketingForDialog, setLastYearMarketingForDialog] = useState<string>("");
  const [lastYearLoyaltyForDialog, setLastYearLoyaltyForDialog] = useState<string>("");
  const [viewButtonLoadingId, setViewButtonLoadingId] = useState<string | null>(null);

  const fetchPromotionData = useCallback(
    async (page: number, limit: number, filter: PromotionFilterParam, showFullScreenLoader = false) => {
      dispatch(setLoading(true));
      if (showFullScreenLoader) {
        dispatch(setFullScreenLoader(true));
      }
      try {
        const data = await fetchPromotion(page, limit, filter);
        const formattedData = data.result.map((item: any) => ({
          ...item,
        }));
        dispatch(setPromotions(formattedData));
        dispatch(setTotalPages(data.totalPages));
        dispatch(setLoading(false));
      } catch (error) {
        console.error("Error fetching Promotions:", error);
      } finally {
        dispatch(setLoading(false));
        if (showFullScreenLoader) {
          dispatch(setFullScreenLoader(false));
        }
      }
    },
    [dispatch]
  );

  useEffect(() => {
    fetchPromotionData(page, limit, filters, true);
  }, [page, limit, fetchPromotionData, filters]);

  // Fetch Filter Config Data
  const fetchConfigData = async () => {
    try {
      await fetchPromotionConfigData();
      // Config data can be used for dropdowns if needed
    } catch (error) {
      console.error("Error fetching config:", error);
    }
  };

  useEffect(() => {
    fetchConfigData();
  }, []);

  const updateExistingRecord = async () => {
    if (!currentPromotionForEdit || !newPromotion) {
      dispatch(
        pushNewAlert({
          type: "error",
          message: "No data to update",
          show: true,
          heading: "Error",
          errMessage: "",
          errDescription: "",
        })
      );
      return;
    }

    const { fiscal_year, fiscal_period, fiscal_week, holidayAndEvents, marketingPromotion, loyaltyCampaigns, lastYearHolidayAndEvents, lastYearMarketingPromotion, lastYearLoyaltyCampaigns } = newPromotion;

    // Check if fiscal keys changed from original
    const fiscalKeysChanged =
      fiscal_year !== currentPromotionForEdit.fiscal_year ||
      fiscal_period !== currentPromotionForEdit.fiscal_period ||
      fiscal_week !== currentPromotionForEdit.fiscal_week;

    // If fiscal keys changed, check for conflicts
    if (fiscalKeysChanged) {
      try {
        const conflictCheck = await getPromotionByFiscal({
          fiscal_year: Number(fiscal_year),
          fiscal_period: String(fiscal_period),
          fiscal_week: String(fiscal_week),
        });

        if (conflictCheck.result && conflictCheck.result["current_year p-week Key"] !== currentPromotionForEdit["current_year p-week Key"]) {
          dispatch(
            pushNewAlert({
              type: "error",
              message: "This fiscal combination already exists for another promotion",
              show: true,
              heading: "Conflict Error",
              errMessage: "",
              errDescription: "",
            })
          );
          return;
        }
      } catch (error) {
        console.error("Error checking for conflicts:", error);
      }
    }

    setIsSaving(true);
    dispatch(setFullScreenLoader(true));

    try {
      await updatePromotion({
        originalKey: currentPromotionForEdit["current_year p-week Key"],
        fiscal_year: Number(fiscal_year),
        fiscal_period: String(fiscal_period),
        fiscal_week: String(fiscal_week),
        holidayAndEvents: String(holidayAndEvents || ""),
        marketingPromotion: String(marketingPromotion || ""),
        loyaltyCampaigns: String(loyaltyCampaigns || ""),
        lastYearHolidayAndEvents: String(lastYearHolidayAndEvents || ""),
        lastYearMarketingPromotion: String(lastYearMarketingPromotion || ""),
        lastYearLoyaltyCampaigns: String(lastYearLoyaltyCampaigns || ""),
      });

      setSaveUpdateConfirmOpen(false);
      setOpenEditPromotion(false);
      setCurrentPromotionForEdit(null);

      await fetchPromotionData(page, limit, filters, false);
      dispatch(
        pushNewAlert({
          type: "success",
          message: "Promotion updated successfully",
          show: true,
          heading: "Success",
          errMessage: "",
          errDescription: "",
        })
      );
    } catch (error) {
      console.error("Error updating promotion:", error);
      dispatch(
        pushNewAlert({
          type: "error",
          message: "Failed to update promotion",
          show: true,
          heading: "Error",
          errMessage: "An error occurred while updating",
          errDescription: error instanceof Error ? error.message : "Unknown error",
        })
      );
    } finally {
      setIsSaving(false);
      dispatch(setFullScreenLoader(false));
    }
  };

  const addManualNewRecord = async () => {
    if (!newPromotion) {
      dispatch(
        pushNewAlert({
          type: "error",
          message: "No data to add",
          show: true,
          heading: "Error",
          errMessage: "",
          errDescription: "",
        })
      );
      return;
    }

    const { fiscal_year, fiscal_period, fiscal_week, holidayAndEvents, marketingPromotion, loyaltyCampaigns } = newPromotion;

    // Check for conflicts before adding
    try {
      const conflictCheck = await getPromotionByFiscal({
        fiscal_year: Number(fiscal_year),
        fiscal_period: String(fiscal_period),
        fiscal_week: String(fiscal_week),
      });

      if (conflictCheck.result) {
        dispatch(
          pushNewAlert({
            type: "error",
            message: "This fiscal combination already exists",
            show: true,
            heading: "Conflict Error",
            errMessage: "",
            errDescription: "",
          })
        );
        return;
      }
    } catch (error) {
      console.error("Error checking for conflicts:", error);
    }

    setIsSaving(true);
    dispatch(setFullScreenLoader(true));

    try {
      await addPromotion({
        fiscal_year: Number(fiscal_year),
        fiscal_period: String(fiscal_period),
        fiscal_week: String(fiscal_week),
        holidayAndEvents: String(holidayAndEvents || ""),
        marketingPromotion: String(marketingPromotion || ""),
        loyaltyCampaigns: String(loyaltyCampaigns || ""),
      });

      setSaveAddConfirmOpen(false);
      setOpenAddManualPromotion(false);
      dispatch(setNewPromotion(initialAddPromotionData));

      await fetchPromotionData(page, limit, filters, false);
      dispatch(
        pushNewAlert({
          type: "success",
          message: "New promotion added successfully",
          show: true,
          heading: "Success",
          errMessage: "",
          errDescription: "",
        })
      );
    } catch (error) {
      console.error("Error adding promotion:", error);
      dispatch(
        pushNewAlert({
          type: "error",
          message: "Failed to add new promotion",
          show: true,
          heading: "Error",
          errMessage: "An error occurred while adding",
          errDescription: error instanceof Error ? error.message : "Unknown error",
        })
      );
    } finally {
      setIsSaving(false);
      dispatch(setFullScreenLoader(false));
      setSaveAddConfirmOpen(false);
      setOpenAddManualPromotion(false);
    }
  };

  //  Edit Record section
  const handleEditBtnClick = async (promotion: PromotionObject) => {
    const promotionId = `${promotion.fiscal_year}-${promotion.fiscal_period}-${promotion.fiscal_week}`;
    setViewButtonLoadingId(promotionId);

    try {
      // Convert PromotionObject to PromotionDetail
      const promotionDetail: PromotionDetail = {
        fiscal_year: promotion.fiscal_year,
        fiscal_period: promotion.fiscal_period,
        fiscal_week: promotion.fiscal_week,
        holidayAndEvents: promotion["Holiday and Events"],
        marketingPromotion: promotion["Marketing Pramotion"], // Note: typo in original data
        loyaltyCampaigns: promotion["Loyalty Campaigns"],
        lastYearHolidayAndEvents: promotion["Last Year Holiday and Events"],
        "current_year p-week Key": promotion["current_year p-week Key"],
      };

      setCurrentPromotionForEdit(promotionDetail);

      // Prepare initial data for the edit form
      const initialEditDataForTab: Partial<PromotionDetail> = {
        fiscal_year: promotionDetail.fiscal_year,
        fiscal_period: promotionDetail.fiscal_period,
        fiscal_week: promotionDetail.fiscal_week,
        holidayAndEvents: promotionDetail.holidayAndEvents,
        marketingPromotion: promotionDetail.marketingPromotion,
        loyaltyCampaigns: promotionDetail.loyaltyCampaigns,
      };

      dispatch(setNewPromotion(initialEditDataForTab as Partial<PromotionDetail>));

      // First check if we have saved last year data in the current record
      const savedLastYearEvents = promotion["Last Year Holiday and Events"];
      const savedLastYearMarketing = promotion["Last Year Marketing Promotion"];
      const savedLastYearLoyalty = promotion["Last Year Loyalty Campaigns"];

      if (savedLastYearEvents || savedLastYearMarketing || savedLastYearLoyalty) {
        // Use the saved last year data
        setLastYearEventsForDialog(savedLastYearEvents || "");
        setLastYearMarketingForDialog(savedLastYearMarketing || "");
        setLastYearLoyaltyForDialog(savedLastYearLoyalty || "");
      } else {
        // Fetch last year data only if no saved data exists
        try {
          const lastYearResponse = await getPromotionByFiscal({
            fiscal_year: promotionDetail.fiscal_year - 1,
            fiscal_period: promotionDetail.fiscal_period,
            fiscal_week: promotionDetail.fiscal_week,
          });

          if (lastYearResponse.result) {
            setLastYearEventsForDialog(lastYearResponse.result["Holiday and Events"] || "");
            setLastYearMarketingForDialog(lastYearResponse.result["Marketing Pramotion"] || "");
            setLastYearLoyaltyForDialog(lastYearResponse.result["Loyalty Campaigns"] || "");
          } else {
            setLastYearEventsForDialog("");
            setLastYearMarketingForDialog("");
            setLastYearLoyaltyForDialog("");
          }
        } catch (error) {
          console.error("Error fetching last year data:", error);
          setLastYearEventsForDialog("");
          setLastYearMarketingForDialog("");
          setLastYearLoyaltyForDialog("");
        }
      }

      setOpenEditPromotion(true);
    } finally {
      setViewButtonLoadingId(null);
    }
  };

  const handleCloseEditPromotionDialog = () => {
    setOpenEditPromotion(false);
    setCurrentPromotionForEdit(null);
    setLastYearEventsForDialog("");
    setLastYearMarketingForDialog("");
    setLastYearLoyaltyForDialog("");
    dispatch(setNewPromotion(initialAddPromotionData));
  };

  const handleEditSaveChanges = () => {
    setSaveUpdateConfirmOpen(true);
  };

  const handleConfirmUpdateSave = async () => {
    await updateExistingRecord();
  };

  // Add Record Section
  const handleAddNewPromotion = () => {
    dispatch(setNewPromotion(initialAddPromotionData));
    setOpenAddManualPromotion(true);
  };

  const handleCloseManualAddPromotionDialog = () => {
    setOpenAddManualPromotion(false);
    dispatch(setNewPromotion(initialAddPromotionData));
  };

  const handleManualAddSaveChanges = () => {
    setSaveAddConfirmOpen(true);
  };

  const handleConfirmManualAddSave = async () => {
    await addManualNewRecord();
  };

  return (
    <ThemeProvider theme={theme}>
      <Box sx={{ height: "calc(100vh - 8%)", display: "flex", flexDirection: "column", overflow: "hidden" }}>
        <StyledPaper elevation={3} sx={{ flexShrink: 0 }}>
          <Grid container spacing={2} alignItems="center">
            <Grid
              item
              xs={12}
              md={6}
              sx={{
                display: "flex",
                justifyContent: "flex-start",
                alignItems: "center",
                gap: 2,
              }}
            >
              <Typography
                variant="h4"
                sx={{
                  flexShrink: 0,
                  whiteSpace: "nowrap",
                  fontWeight: 500,
                  letterSpacing: "0.05em",
                  color: "primary.main",
                }}
              >
                {tablename}
              </Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2 }}>
                {(userRole?.includes(ROLES.ADMIN) || userRole?.includes(ROLES.DEVELOPER)) && (
                  <StyledButton variant="contained" color="primary" onClick={handleAddNewPromotion} startIcon={<AddIcon />}>
                    Add New Record
                  </StyledButton>
                )}
              </Box>
            </Grid>
          </Grid>
        </StyledPaper>

        <Box sx={{ flexGrow: 1, display: "flex", flexDirection: "column", overflow: "auto", width: "100%" }}>
          <PromotionTable promotions={promotions} loading={loading} limit={limit} handleClickOpen={handleEditBtnClick} loadingRowId={viewButtonLoadingId} />
        </Box>
      </Box>

      {/* Edit Promotion Dialog */}
      <Dialog open={openEditPromotion} onClose={handleCloseEditPromotionDialog} maxWidth="xl" fullWidth>
        <DialogTitle>Edit Promotion Record</DialogTitle>
        <DialogContent dividers>
          <AddPromotionTab
            onSaveBtnCallback={handleEditSaveChanges}
            onCancelBtnCallback={handleCloseEditPromotionDialog}
            initialDataForEdit={currentPromotionForEdit || undefined}
            lastYearEventsForDisplay={lastYearEventsForDialog}
            lastYearMarketingForDisplay={lastYearMarketingForDialog}
            lastYearLoyaltyForDisplay={lastYearLoyaltyForDialog}
          />
        </DialogContent>
      </Dialog>

      {/* Add Promotion Dialog */}
      <Dialog open={openAddManualPromotion} onClose={handleCloseManualAddPromotionDialog} maxWidth="xl" fullWidth>
        <DialogTitle>Add Promotion Record</DialogTitle>
        <DialogContent dividers>
          <AddPromotionTab onSaveBtnCallback={handleManualAddSaveChanges} onCancelBtnCallback={handleCloseManualAddPromotionDialog} />
        </DialogContent>
      </Dialog>

      {/* Save Edit Confirmation Dialog */}
      <Dialog open={saveUpdateConfirmOpen} onClose={() => setSaveUpdateConfirmOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Confirm Changes</DialogTitle>
        <DialogContent>
          <Typography
            sx={{
              fontSize: "1.2rem",
            }}
            variant="body1"
            gutterBottom
          >
            You are about to update this promotion record with the following changes:
          </Typography>
          {newPromotion && currentPromotionForEdit && (
            <Box sx={{ mt: 2, display: "flex", flexDirection: "column", gap: 1 }}>
              {Object.entries(newPromotion).map(([key, value]) => {
                const originalValue = currentPromotionForEdit[key as keyof PromotionDetail];
                if (value !== originalValue) {
                  return (
                    <Box key={key} sx={{ p: 1, backgroundColor: "rgba(255, 255, 255, 0.05)", borderRadius: 1 }}>
                      <Typography variant="body2" sx={{ fontWeight: 500 }}>
                        {addSpaceToCamelCase(key)}: {String(originalValue)} → {String(value)}
                      </Typography>
                    </Box>
                  );
                }
                return null;
              })}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSaveUpdateConfirmOpen(false)} disabled={isSaving}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmUpdateSave}
            variant="contained"
            color="primary"
            disabled={isSaving}
            startIcon={isSaving ? <CircularProgress size={20} color="inherit" /> : null}
          >
            {isSaving ? "Saving..." : "Confirm Save"}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Save Add Confirmation Dialog */}
      <Dialog open={saveAddConfirmOpen} onClose={() => setSaveAddConfirmOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Add Promotion</DialogTitle>
        <DialogContent>
          <Typography
            sx={{
              fontSize: "1.2rem",
            }}
            variant="body1"
            gutterBottom
          >
            You are about to add a new promotion record
          </Typography>
          {newPromotion && (
            <Box sx={{ mt: 2, display: "flex", flexDirection: "column", gap: 1 }}>
              {Object.entries(newPromotion).map(([key, value]) => (
                <Box key={key} sx={{ p: 1, backgroundColor: "rgba(255, 255, 255, 0.05)", borderRadius: 1 }}>
                  <Typography variant="body2" sx={{ fontWeight: 500 }}>
                    {addSpaceToCamelCase(key)}: {String(value)}
                  </Typography>
                </Box>
              ))}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSaveAddConfirmOpen(false)} disabled={isSaving}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmManualAddSave}
            variant="contained"
            color="primary"
            disabled={isSaving}
            startIcon={isSaving ? <CircularProgress size={20} color="inherit" /> : null}
          >
            {isSaving ? "Saving..." : "Add Promotion"}
          </Button>
        </DialogActions>
      </Dialog>
    </ThemeProvider>
  );
};

export default Promotion;
