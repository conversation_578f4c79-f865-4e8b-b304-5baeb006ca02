import { Box, Button, CircularProgress, Grid, InputAdornment, TextField } from "@mui/material";
import React, { useEffect, useState, useCallback } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../../store";
import SaveIcon from "@mui/icons-material/Save";
import { AddStoreFormFieldType, FormObjectValidation } from "../../../store-logistic-manager/Components/AddStoreHelper";
import debounce from "lodash/debounce";
import { setNewPromotion } from "../../../../../__redux/addPromotionSlice";
import { pushNewAlert } from "../../../../../__redux/generalSlice";
import { getPromotionByFiscal, PromotionDetail } from "../../../../api/promotionApi";
import { ROLES } from "../../../../constants/constants";

interface AddPromotionProps {
  onSaveBtnCallback: () => void;
  onCancelBtnCallback: () => void;
  initialDataForEdit?: Partial<PromotionDetail>;
  lastYearEventsForDisplay?: string;
  lastYearMarketingForDisplay?: string;
  lastYearLoyaltyForDisplay?: string;
}

interface EditedData {
  fiscal_year?: number | null;
  fiscal_period?: string | null;
  fiscal_week?: string | null;
  holidayAndEvents?: string | null;
  marketingPromotion?: string | null;
  loyaltyCampaigns?: string | null;
  lastYearHolidayAndEvents?: string | null;
  lastYearMarketingPromotion?: string | null;
  lastYearLoyaltyCampaigns?: string | null;
}

export const formatTableName = (tableName: string): string => {
  return tableName
    .split("_")
    .map(word => {
      if (word.toLowerCase() === "str") {
        return "STR";
      }
      return word.charAt(0).toUpperCase() + word.slice(1);
    })
    .join(" ");
};

const FormObjectRule: Record<string, FormObjectValidation> = {
  fiscal_year: {
    helperText: "",
    maxInputLimit: 4,
    isPrimaryKey: true,
    fieldType: AddStoreFormFieldType.NUMERIC,
    isRequired: true,
  },
  fiscal_period: {
    helperText: "",
    maxInputLimit: 2,
    isPrimaryKey: true,
    fieldType: AddStoreFormFieldType.TEXT,
    isRequired: true,
    staticPrefix: "P",
  },
  fiscal_week: {
    helperText: "",
    maxInputLimit: 2,
    isPrimaryKey: true,
    fieldType: AddStoreFormFieldType.TEXT,
    isRequired: true,
    staticPrefix: "Week ",
  },
  holidayAndEvents: {
    helperText: "",
    maxInputLimit: 500,
    isPrimaryKey: false,
    fieldType: AddStoreFormFieldType.TEXT,
    isRequired: false,
  },
  marketingPromotion: {
    helperText: "",
    maxInputLimit: 500,
    isPrimaryKey: false,
    fieldType: AddStoreFormFieldType.TEXT,
    isRequired: false,
  },
  lastYearHolidayAndEvents: {
    helperText: "",
    maxInputLimit: 500,
    isPrimaryKey: false,
    fieldType: AddStoreFormFieldType.TEXT,
    isRequired: false,
    isDisabled: true,
  },
  lastYearMarketingPromotion: {
    helperText: "",
    maxInputLimit: 500,
    isPrimaryKey: false,
    fieldType: AddStoreFormFieldType.TEXT,
    isRequired: false,
    isDisabled: true,
  },
  loyaltyCampaigns: {
    helperText: "",
    maxInputLimit: 500,
    isPrimaryKey: false,
    fieldType: AddStoreFormFieldType.TEXT,
    isRequired: false,
  },
  lastYearLoyaltyCampaigns: {
    helperText: "",
    maxInputLimit: 500,
    isPrimaryKey: false,
    fieldType: AddStoreFormFieldType.TEXT,
    isRequired: false,
    isDisabled: true,
  },
};

const AddPromotionTab: React.FC<AddPromotionProps> = ({
  onSaveBtnCallback,
  onCancelBtnCallback,
  initialDataForEdit,
  lastYearEventsForDisplay = "",
  lastYearMarketingForDisplay = "",
  lastYearLoyaltyForDisplay = "",
}) => {
  const dispatch = useDispatch();
  const { newPromotion } = useSelector((state: RootState) => state.addPromotion);
  const { userRole } = useSelector((state: RootState) => state.auth);

  const [editedData, setEditedData] = useState<EditedData>({});
  const [lastYearEvents, setLastYearEvents] = useState<string>(lastYearEventsForDisplay);
  const [lastYearMarketing, setLastYearMarketing] = useState<string>(lastYearMarketingForDisplay);
  const [lastYearLoyalty, setLastYearLoyalty] = useState<string>(lastYearLoyaltyForDisplay);
  const isEditMode = !!initialDataForEdit;
  const [fieldRules, setFieldRules] = useState<Record<string, FormObjectValidation>>(() => {
    const rules = JSON.parse(JSON.stringify(FormObjectRule));
    if (isEditMode) {
      rules.lastYearHolidayAndEvents.isDisabled = false;
      rules.lastYearMarketingPromotion.isDisabled = false;
      rules.lastYearLoyaltyCampaigns.isDisabled = false;
    }
    return rules;
  });
  const [isLoading] = useState(false);
  const [isFiscalCombinationDuplicate, setIsFiscalCombinationDuplicate] = useState(false);
  const [isIncompleteFiscalInput, setIsIncompleteFiscalInput] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [isLoadingLastYearData, setIsLoadingLastYearData] = useState(false);

  useEffect(() => {
    if (initialDataForEdit) {
      setEditedData(initialDataForEdit);
    } else {
      setEditedData(newPromotion);
    }
  }, [newPromotion, initialDataForEdit]);

  useEffect(() => {
    setLastYearEvents(lastYearEventsForDisplay);
  }, [lastYearEventsForDisplay]);

  useEffect(() => {
    setLastYearMarketing(lastYearMarketingForDisplay);
  }, [lastYearMarketingForDisplay]);

  useEffect(() => {
    setLastYearLoyalty(lastYearLoyaltyForDisplay);
  }, [lastYearLoyaltyForDisplay]);

  // Check for incomplete fiscal input
  useEffect(() => {
    const period = typeof editedData.fiscal_period === "string" ? editedData.fiscal_period.toLowerCase() : "";
    const week = typeof editedData.fiscal_week === "string" ? editedData.fiscal_week.toLowerCase() : "";

    const isPeriodIncomplete = period === "p" || period === "";
    const isWeekIncomplete = week === "week" || week === "week " || week === "";

    setIsIncompleteFiscalInput(isPeriodIncomplete || isWeekIncomplete);
  }, [editedData.fiscal_period, editedData.fiscal_week]);

  const fetchLastYearData = useCallback(
    debounce(async (fiscal_year: number, fiscal_period: string, fiscal_week: string) => {
      if (!fiscal_year || !fiscal_period || !fiscal_week) return;

      // Skip if we already have last year data for edit mode
      if (
        initialDataForEdit &&
        lastYearEventsForDisplay &&
        fiscal_year === initialDataForEdit.fiscal_year &&
        fiscal_period === initialDataForEdit.fiscal_period &&
        fiscal_week === initialDataForEdit.fiscal_week
      ) {
        return;
      }

      setIsLoadingLastYearData(true);
      try {
        const response = await getPromotionByFiscal({
          fiscal_year: fiscal_year - 1,
          fiscal_period,
          fiscal_week,
        });

        if (response.result) {
          setLastYearEvents(response.result["Holiday and Events"] || "");
          setLastYearMarketing(response.result["Marketing Pramotion"] || "");
          setLastYearLoyalty(response.result["Loyalty Campaigns"] || "");
        } else {
          setLastYearEvents("");
          setLastYearMarketing("");
          setLastYearLoyalty("");
        }
      } catch (error) {
        console.error("Error fetching last year data:", error);
        setLastYearEvents("");
        setLastYearMarketing("");
        setLastYearLoyalty("");
      } finally {
        setIsLoadingLastYearData(false);
      }
    }, 500),
    [initialDataForEdit, lastYearEventsForDisplay]
  );

  const validateExistingValue = useCallback(
    async (fiscal_year: number, fiscal_period: string, fiscal_week: string) => {
      // Check if in edit mode and the fiscal key values are same as original
      if (initialDataForEdit) {
        const isBackToOriginal =
          fiscal_year === initialDataForEdit.fiscal_year &&
          fiscal_period === initialDataForEdit.fiscal_period &&
          fiscal_week === initialDataForEdit.fiscal_week;

        if (isBackToOriginal) {
          setIsFiscalCombinationDuplicate(false);
          setFieldRules(prev => ({
            ...prev,
            fiscal_year: { ...prev.fiscal_year, helperText: "" },
            fiscal_period: { ...prev.fiscal_period, helperText: "" },
            fiscal_week: { ...prev.fiscal_week, helperText: "" },
          }));
          return;
        }
      }

      if (!fiscal_year || !fiscal_period || !fiscal_week) {
        setIsFiscalCombinationDuplicate(false);
        setFieldRules(prev => ({
          ...prev,
          fiscal_year: { ...prev.fiscal_year, helperText: "" },
          fiscal_period: { ...prev.fiscal_period, helperText: "" },
          fiscal_week: { ...prev.fiscal_week, helperText: "" },
        }));
        return;
      }

      setIsValidating(true);
      try {
        const response = await getPromotionByFiscal({
          fiscal_year: Number(fiscal_year),
          fiscal_period: String(fiscal_period),
          fiscal_week: String(fiscal_week),
        });

        if (response.result) {
          // For add mode: any result found is a duplicate
          // For edit mode: result is duplicate only if it's a different record
          let isDuplicate = false;

          if (!initialDataForEdit) {
            // Add mode - any existing record is a duplicate
            isDuplicate = true;
          } else {
            // Edit mode - only duplicate if it's a different record
            isDuplicate = response.result["current_year p-week Key"] !== initialDataForEdit["current_year p-week Key"];
          }

          if (isDuplicate) {
            setIsFiscalCombinationDuplicate(true);
            const helperText = "This combination of Fiscal Year, Period, and Week already exists.";
            setFieldRules(prev => ({
              ...prev,
              fiscal_year: { ...prev.fiscal_year, helperText },
              fiscal_period: { ...prev.fiscal_period, helperText },
              fiscal_week: { ...prev.fiscal_week, helperText },
            }));
          } else {
            setIsFiscalCombinationDuplicate(false);
            setFieldRules(prev => ({
              ...prev,
              fiscal_year: { ...prev.fiscal_year, helperText: "" },
              fiscal_period: { ...prev.fiscal_period, helperText: "" },
              fiscal_week: { ...prev.fiscal_week, helperText: "" },
            }));
          }
        } else {
          // No existing record found - not a duplicate
          setIsFiscalCombinationDuplicate(false);
          setFieldRules(prev => ({
            ...prev,
            fiscal_year: { ...prev.fiscal_year, helperText: "" },
            fiscal_period: { ...prev.fiscal_period, helperText: "" },
            fiscal_week: { ...prev.fiscal_week, helperText: "" },
          }));
        }
      } catch (error) {
        console.error("Error validating existing value:", error);
        setIsFiscalCombinationDuplicate(false);
        setFieldRules(prev => ({
          ...prev,
          fiscal_year: { ...prev.fiscal_year, helperText: "" },
          fiscal_period: { ...prev.fiscal_period, helperText: "" },
          fiscal_week: { ...prev.fiscal_week, helperText: "" },
        }));
      } finally {
        setIsValidating(false);
      }
    },
    [initialDataForEdit]
  );

  const debouncedValidateExistingValue = useCallback(
    debounce(async (fiscal_year: number, fiscal_period: string, fiscal_week: string) => {
      await validateExistingValue(fiscal_year, fiscal_period, fiscal_week);
    }, 400),
    [validateExistingValue]
  );

  // Validate on initial load or when initial data changes
  useEffect(() => {
    if (editedData.fiscal_year && editedData.fiscal_period && editedData.fiscal_week) {
      validateExistingValue(Number(editedData.fiscal_year), String(editedData.fiscal_period), String(editedData.fiscal_week));
    }
  }, [editedData.fiscal_year, editedData.fiscal_period, editedData.fiscal_week, validateExistingValue]);

  const handleInputChange = (key: string, value: string | number | null) => {
    let finalValue = value;

    // Handle static prefix fields
    const rule = fieldRules[key];
    if (rule?.staticPrefix && typeof value === "string") {
      finalValue = rule.staticPrefix + value;
    }

    const updatedData = { ...editedData, [key]: finalValue };
    setEditedData(updatedData);

    // Trigger validation for fiscal primary keys
    if (["fiscal_year", "fiscal_period", "fiscal_week"].includes(key)) {
      // Clear fiscal combination errors if any fiscal key is cleared
      if (!finalValue) {
        setIsFiscalCombinationDuplicate(false);
        setFieldRules(prev => ({
          ...prev,
          fiscal_year: { ...prev.fiscal_year, helperText: "" },
          fiscal_period: { ...prev.fiscal_period, helperText: "" },
          fiscal_week: { ...prev.fiscal_week, helperText: "" },
        }));
      } else if (updatedData.fiscal_year && updatedData.fiscal_period && updatedData.fiscal_week) {
        // Only validate if all three fiscal keys have values
        debouncedValidateExistingValue(Number(updatedData.fiscal_year), String(updatedData.fiscal_period), String(updatedData.fiscal_week));
      }

      // Fetch last year data if all fiscal keys are present
      if (updatedData.fiscal_year && updatedData.fiscal_period && updatedData.fiscal_week) {
        fetchLastYearData(Number(updatedData.fiscal_year), String(updatedData.fiscal_period), String(updatedData.fiscal_week));
      }
    }
  };

  const validateInputOnSave = (): string | null => {
    // Check for helper text errors
    const hasHelperTextErrors = Object.values(fieldRules).some(rule => rule.helperText !== "");
    if (hasHelperTextErrors) {
      return "Please check all fields, there are validation errors.";
    }

    // Check required fields
    const requiredFields = Object.entries(fieldRules)
      .filter(([, rule]) => rule.isRequired)
      .map(([key]) => key);

    for (const field of requiredFields) {
      if (!editedData[field as keyof EditedData] || editedData[field as keyof EditedData] === "") {
        return `Please fill in the required field: ${field.replace(/([A-Z])/g, " $1").trim()}`;
      }
    }

    // Check incomplete fiscal inputs
    const period = typeof editedData.fiscal_period === "string" ? editedData.fiscal_period.toLowerCase() : "";
    const week = typeof editedData.fiscal_week === "string" ? editedData.fiscal_week.toLowerCase() : "";

    if (period === "p" || period === "") {
      return "Please enter a valid fiscal period number.";
    }

    if (week === "week" || week === "week " || week === "") {
      return "Please enter a valid fiscal week number.";
    }

    return null;
  };

  const handleSave = () => {
    const errorStr = validateInputOnSave();
    if (errorStr) {
      dispatch(
        pushNewAlert({
          type: "error",
          message: errorStr,
          show: true,
          heading: "Validation Error",
          errMessage: "",
          errDescription: "",
        })
      );
      return;
    }

    const current_year_p_week_key = `${editedData.fiscal_year}—${editedData.fiscal_period}-${editedData.fiscal_week}`;
    const finalData = {
      ...editedData,
      "current_year p-week Key": current_year_p_week_key,
    };

    if (isEditMode) {
      finalData.lastYearHolidayAndEvents = lastYearEvents;
      finalData.lastYearMarketingPromotion = lastYearMarketing;
      finalData.lastYearLoyaltyCampaigns = lastYearLoyalty;
    }

    dispatch(setNewPromotion(finalData));
    onSaveBtnCallback();
  };

  const renderFormInputField = (key: string, value: unknown) => {
    const rule = fieldRules[key];

    if (key === "lastYearHolidayAndEvents") {
      return (
        <TextField
          key={key}
          fullWidth
          label="Last Year Holiday and Events"
          value={lastYearEvents}
          disabled={!isEditMode}
          onChange={e => {
            setLastYearEvents(e.target.value);
            handleInputChange(key, e.target.value);
          }}
          multiline
          rows={3}
          variant="outlined"
          sx={{ mb: 2 }}
          InputProps={{
            endAdornment: isLoadingLastYearData ? (
              <InputAdornment position="end">
                <CircularProgress size={20} />
              </InputAdornment>
            ) : undefined,
          }}
          helperText={isLoadingLastYearData ? "Loading last year data..." : undefined}
        />
      );
    }

    if (key === "lastYearMarketingPromotion") {
      return (
        <TextField
          key={key}
          fullWidth
          label="Last Year Marketing Promotion"
          value={lastYearMarketing}
          disabled={!isEditMode}
          onChange={e => {
            setLastYearMarketing(e.target.value);
            handleInputChange(key, e.target.value);
          }}
          multiline
          rows={3}
          variant="outlined"
          sx={{ mb: 2 }}
          InputProps={{
            endAdornment: isLoadingLastYearData ? (
              <InputAdornment position="end">
                <CircularProgress size={20} />
              </InputAdornment>
            ) : undefined,
          }}
          helperText={isLoadingLastYearData ? "Loading last year data..." : undefined}
        />
      );
    }

    if (key === "lastYearLoyaltyCampaigns") {
      return (
        <TextField
          key={key}
          fullWidth
          label="Last Year Loyalty Campaigns"
          value={lastYearLoyalty}
          disabled={!isEditMode}
          onChange={e => {
            setLastYearLoyalty(e.target.value);
            handleInputChange(key, e.target.value);
          }}
          multiline
          rows={3}
          variant="outlined"
          sx={{ mb: 2 }}
          InputProps={{
            endAdornment: isLoadingLastYearData ? (
              <InputAdornment position="end">
                <CircularProgress size={20} />
              </InputAdornment>
            ) : undefined,
          }}
          helperText={isLoadingLastYearData ? "Loading last year data..." : undefined}
        />
      );
    }

    const isFiscalField = ["fiscal_year", "fiscal_period", "fiscal_week"].includes(key);
    const showValidationLoader = isFiscalField && isValidating;

    if (rule?.staticPrefix && (key === "fiscal_period" || key === "fiscal_week")) {
      const displayValue = value ? String(value).replace(rule.staticPrefix, "") : "";

      return (
        <TextField
          key={key}
          fullWidth
          label={key.replace(/([A-Z])/g, " $1").trim()}
          value={displayValue}
          onChange={e => handleInputChange(key, e.target.value)}
          error={rule.helperText !== ""}
          helperText={showValidationLoader ? "Checking availability..." : rule.helperText}
          disabled={rule.isDisabled}
          required={rule.isRequired}
          InputProps={{
            startAdornment: <InputAdornment position="start">{rule.staticPrefix}</InputAdornment>,
            endAdornment: showValidationLoader ? (
              <InputAdornment position="end">
                <CircularProgress size={16} />
              </InputAdornment>
            ) : undefined,
          }}
          inputProps={{
            maxLength: rule.maxInputLimit,
          }}
          variant="outlined"
          sx={{ mb: 2 }}
        />
      );
    }

    return (
      <TextField
        key={key}
        fullWidth
        label={key.replace(/([A-Z])/g, " $1").trim()}
        value={value || ""}
        onChange={e => {
          const inputValue = rule.fieldType === AddStoreFormFieldType.NUMERIC ? Number(e.target.value) || null : e.target.value;
          handleInputChange(key, inputValue);
        }}
        error={rule.helperText !== ""}
        helperText={showValidationLoader ? "Checking availability..." : rule.helperText}
        disabled={rule.isDisabled}
        required={rule.isRequired}
        type={rule.fieldType === AddStoreFormFieldType.NUMERIC ? "number" : "text"}
        multiline={key.includes("Events") || key.includes("Promotion") || key.includes("Campaigns")}
        rows={key.includes("Events") || key.includes("Promotion") || key.includes("Campaigns") ? 3 : 1}
        inputProps={{
          maxLength: rule.fieldType === AddStoreFormFieldType.TEXT ? rule.maxInputLimit : undefined,
        }}
        InputProps={{
          endAdornment: showValidationLoader ? (
            <InputAdornment position="end">
              <CircularProgress size={16} />
            </InputAdornment>
          ) : undefined,
        }}
        variant="outlined"
        sx={{ mb: 2 }}
      />
    );
  };

  const isFormValid = !isLoading && !isFiscalCombinationDuplicate && !isIncompleteFiscalInput && (Object.keys(editedData).length > 0 || initialDataForEdit);

  return (
    <Box sx={{ p: 2 }}>
      <Grid container spacing={2}>
        {Object.entries(FormObjectRule).map(([key]) => (
          <Grid item xs={12} md={6} key={key}>
            {renderFormInputField(key, editedData[key as keyof EditedData])}
          </Grid>
        ))}
      </Grid>

      <Box sx={{ display: "flex", justifyContent: "flex-end", gap: 2, mt: 3 }}>
        <Button variant="outlined" onClick={onCancelBtnCallback} disabled={isLoading}>
          Cancel
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={handleSave}
          disabled={!isFormValid || (!userRole?.includes(ROLES.ADMIN) && !userRole?.includes(ROLES.DEVELOPER))}
          startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : <SaveIcon />}
        >
          {isLoading ? "Saving..." : "Save"}
        </Button>
      </Box>
    </Box>
  );
};

export default AddPromotionTab;
